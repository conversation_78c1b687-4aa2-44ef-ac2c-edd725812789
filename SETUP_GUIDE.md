# Quick Setup Guide for ComfyUI + Langflow Integration

## Step 1: Install Dependencies (Skip chroma-hnswlib)

Instead of the full requirements.txt, install only what we need:

```bash
pip install aiohttp websockets
```

## Step 2: Create the Custom Component

1. In Langflow, go to the **Components** section
2. Click **"+ New Component"** or **"Custom Component"**
3. Copy and paste the entire content of `comfyui_image_generator.py`
4. Save the component

## Step 3: Create a Simple Flow

Instead of importing the JSON (which has issues), create manually:

1. **Start a new flow** in Langflow
2. **Add components**:
   - Drag **Chat Input** to the canvas
   - Drag **Chat Output** to the canvas  
   - Find your **ComfyUI Image Generator** in the custom components and add it

3. **Connect them**:
   - Connect **Chat Input** → **ComfyUI Image Generator** (prompt input)
   - Connect **ComfyUI Image Generator** → **Chat Output** (image output)

## Step 4: Configure the ComfyUI Component

Click on the ComfyUI Image Generator component and set:

- **Prompt**: Leave empty (will come from chat input)
- **ComfyUI Host**: `127.0.0.1:8188` (or your ComfyUI server address)
- **Workflow Path**: `Image Generation.json`
- **Return Base64**: `True`

## Step 5: Test the Setup

1. Make sure ComfyUI is running on port 8188
2. Make sure `Image Generation.json` is in the same directory as Langflow
3. Start the flow
4. Type a prompt like: "a beautiful sunset over mountains"
5. The image should appear in the chat

## Troubleshooting

### "Extra inputs are not permitted" Error
- Make sure you're using the corrected `comfyui_image_generator.py` file
- Try creating a new custom component from scratch
- Check that all imports are correct

### ComfyUI Connection Issues
- Verify ComfyUI is running: `http://localhost:8188`
- Check the host/port settings in the component
- Make sure the workflow JSON file exists

### Image Not Displaying
- Ensure `return_base64` is set to `True`
- Check that the Chat Output component is properly connected
- Look at the component logs for error messages

## Alternative: Manual Component Creation

If pasting the code doesn't work, create the component step by step:

1. Create a new custom component
2. Set the basic properties:
   ```python
   display_name = "ComfyUI Image Generator"
   description = "Generate images using ComfyUI"
   icon = "image"
   ```

3. Add inputs one by one:
   ```python
   inputs = [
       MultilineInput(name="prompt", display_name="Prompt"),
       StrInput(name="comfyui_host", display_name="ComfyUI Host", value="127.0.0.1:8188"),
       # ... add others as needed
   ]
   ```

4. Add the output:
   ```python
   outputs = [
       Output(name="image_message", display_name="Generated Image", method="generate_image")
   ]
   ```

5. Add the methods one by one, starting with the simplest ones

## Minimal Working Version

If you're still having issues, here's a minimal version to test:

```python
from langflow.custom import Component
from langflow.io import StrInput, Output
from langflow.schema import Message

class SimpleComfyUI(Component):
    display_name = "Simple ComfyUI"
    description = "Basic ComfyUI integration"
    
    inputs = [
        StrInput(name="prompt", display_name="Prompt")
    ]
    
    outputs = [
        Output(name="result", display_name="Result", method="generate")
    ]
    
    def generate(self) -> Message:
        return Message(text=f"Would generate image for: {self.prompt}")
```

This will at least test if the component system is working before adding the full ComfyUI integration.
