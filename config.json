{"comfyui": {"host": "127.0.0.1", "port": 8188, "timeout": 60, "workflow_path": "Image Generation.json"}, "generation": {"default_width": 512, "default_height": 512, "default_steps": 20, "default_cfg": 8.0, "default_negative_prompt": "text, watermark, blurry, low quality"}, "output": {"return_base64": true, "save_to_file": false, "output_directory": "./generated_images"}, "presets": {"fast": {"steps": 10, "cfg": 6.0, "description": "Quick generation with lower quality"}, "balanced": {"steps": 20, "cfg": 8.0, "description": "Good balance of speed and quality"}, "detailed": {"steps": 30, "cfg": 10.0, "description": "High quality with longer generation time"}}, "example_prompts": ["a beautiful sunset over mountains, landscape photography", "a futuristic city with flying cars, cyberpunk style", "a cozy cottage in a flower garden, spring time", "a majestic dragon flying over a medieval castle, fantasy art", "a serene lake with reflections, peaceful nature scene"]}