import json
import uuid
import base64
import asyncio
import aiohttp
import websockets
from typing import Optional, Dict, Any
from langflow.custom import Component
from langflow.io import StrInput, IntInput, FloatInput, BoolInput, Output, MultilineInput
from langflow.schema import Message


class ComfyUIImageGenerator(Component):
    display_name = "ComfyUI Image Generator"
    description = "Generate images using ComfyUI API with text prompts"
    icon = "image"
    name = "ComfyUIImageGenerator"
    
    inputs = [
        MultilineInput(
            name="prompt",
            display_name="Prompt",
            info="Text prompt for image generation",
            value="beautiful scenery nature glass bottle landscape, purple galaxy bottle"
        ),
        StrInput(
            name="negative_prompt",
            display_name="Negative Prompt",
            info="Negative prompt to avoid certain elements",
            value="text, watermark",
            advanced=True
        ),
        StrInput(
            name="comfyui_host",
            display_name="ComfyUI Host",
            info="ComfyUI server host (e.g., 127.0.0.1:8188)",
            value="127.0.0.1:8188"
        ),
        StrInput(
            name="workflow_path",
            display_name="Workflow Path",
            info="Path to ComfyUI workflow JSON file",
            value="Image Generation.json",
            advanced=True
        ),
        IntInput(
            name="width",
            display_name="Width",
            info="Image width in pixels",
            value=512,
            advanced=True
        ),
        IntInput(
            name="height",
            display_name="Height",
            info="Image height in pixels",
            value=512,
            advanced=True
        ),
        IntInput(
            name="steps",
            display_name="Steps",
            info="Number of sampling steps",
            value=20,
            advanced=True
        ),
        FloatInput(
            name="cfg",
            display_name="CFG Scale",
            info="Classifier-free guidance scale",
            value=8.0,
            advanced=True
        ),
        IntInput(
            name="timeout",
            display_name="Timeout (seconds)",
            info="Maximum time to wait for image generation",
            value=60,
            advanced=True
        ),
        BoolInput(
            name="return_base64",
            display_name="Return Base64",
            info="Return image as base64 string in message",
            value=True,
            advanced=True
        )
    ]
    
    outputs = [
        Output(
            display_name="Generated Image",
            name="image_message",
            method="generate_image"
        )
    ]

    def load_workflow(self) -> Dict[str, Any]:
        """Load ComfyUI workflow from JSON file"""
        try:
            with open(self.workflow_path, 'r') as file:
                workflow = json.load(file)
                return workflow
        except FileNotFoundError:
            raise ValueError(f"Workflow file not found: {self.workflow_path}")
        except json.JSONDecodeError:
            raise ValueError(f"Invalid JSON in workflow file: {self.workflow_path}")

    def modify_workflow(self, workflow: Dict[str, Any]) -> Dict[str, Any]:
        """Modify workflow with current parameters"""
        # Find nodes by class type
        id_to_class_type = {id: details['class_type'] for id, details in workflow.items()}
        
        # Find KSampler node
        k_sampler_id = None
        for node_id, class_type in id_to_class_type.items():
            if class_type == 'KSampler':
                k_sampler_id = node_id
                break
        
        if not k_sampler_id:
            raise ValueError("No KSampler node found in workflow")
        
        # Generate random seed
        workflow[k_sampler_id]['inputs']['seed'] = uuid.uuid4().int & (1<<63)-1
        workflow[k_sampler_id]['inputs']['steps'] = self.steps
        workflow[k_sampler_id]['inputs']['cfg'] = self.cfg
        
        # Find positive prompt node
        positive_input_id = workflow[k_sampler_id]['inputs']['positive'][0]
        workflow[positive_input_id]['inputs']['text'] = self.prompt
        
        # Find negative prompt node  
        negative_input_id = workflow[k_sampler_id]['inputs']['negative'][0]
        workflow[negative_input_id]['inputs']['text'] = self.negative_prompt
        
        # Find EmptyLatentImage node and set dimensions
        for node_id, class_type in id_to_class_type.items():
            if class_type == 'EmptyLatentImage':
                workflow[node_id]['inputs']['width'] = self.width
                workflow[node_id]['inputs']['height'] = self.height
                break
        
        return workflow

    async def queue_prompt(self, workflow: Dict[str, Any], client_id: str) -> str:
        """Queue prompt to ComfyUI and return prompt_id"""
        url = f"http://{self.comfyui_host}/prompt"
        data = {
            "prompt": workflow,
            "client_id": client_id
        }

        async with aiohttp.ClientSession() as session:
            async with session.post(url, json=data) as response:
                if response.status != 200:
                    raise Exception(f"Failed to queue prompt: {response.status}")
                result = await response.json()
                return result['prompt_id']

    async def wait_for_completion(self, prompt_id: str, client_id: str) -> None:
        """Wait for prompt completion via WebSocket"""
        ws_url = f"ws://{self.comfyui_host}/ws?clientId={client_id}"

        try:
            async with websockets.connect(ws_url) as websocket:
                while True:
                    message = await asyncio.wait_for(websocket.recv(), timeout=self.timeout)

                    if isinstance(message, str):
                        data = json.loads(message)

                        if data['type'] == 'executing':
                            node_data = data['data']
                            if node_data['node'] is None and node_data['prompt_id'] == prompt_id:
                                # Execution completed
                                break
                        elif data['type'] == 'progress':
                            # Update status with progress
                            progress_data = data['data']
                            self.status = f"Generating... Step {progress_data['value']}/{progress_data['max']}"

        except asyncio.TimeoutError:
            raise Exception(f"Timeout waiting for image generation ({self.timeout}s)")
        except Exception as e:
            raise Exception(f"WebSocket error: {str(e)}")

    async def get_image(self, prompt_id: str) -> bytes:
        """Get generated image from ComfyUI"""
        # Get history
        history_url = f"http://{self.comfyui_host}/history/{prompt_id}"

        async with aiohttp.ClientSession() as session:
            async with session.get(history_url) as response:
                if response.status != 200:
                    raise Exception(f"Failed to get history: {response.status}")
                history = await response.json()

        # Find output images
        if prompt_id not in history:
            raise Exception("Prompt not found in history")

        outputs = history[prompt_id]['outputs']

        # Look for SaveImage node output
        for node_id, node_output in outputs.items():
            if 'images' in node_output:
                for image_info in node_output['images']:
                    if image_info['type'] == 'output':
                        # Get the image
                        image_url = f"http://{self.comfyui_host}/view"
                        params = {
                            'filename': image_info['filename'],
                            'subfolder': image_info['subfolder'],
                            'type': image_info['type']
                        }

                        async with aiohttp.ClientSession() as session:
                            async with session.get(image_url, params=params) as response:
                                if response.status != 200:
                                    raise Exception(f"Failed to get image: {response.status}")
                                return await response.read()

        raise Exception("No output image found")

    async def generate_image_async(self) -> Message:
        """Main async method to generate image"""
        try:
            self.status = "Loading workflow..."
            workflow = self.load_workflow()

            self.status = "Modifying workflow..."
            modified_workflow = self.modify_workflow(workflow)

            client_id = str(uuid.uuid4())

            self.status = "Queuing prompt..."
            prompt_id = await self.queue_prompt(modified_workflow, client_id)

            self.status = "Waiting for completion..."
            await self.wait_for_completion(prompt_id, client_id)

            self.status = "Retrieving image..."
            image_data = await self.get_image(prompt_id)

            if self.return_base64:
                # Convert to base64 for display in chat
                image_b64 = base64.b64encode(image_data).decode('utf-8')
                image_url = f"data:image/png;base64,{image_b64}"

                self.status = f"Generated image successfully! ({len(image_data)} bytes)"
                return Message(
                    text=f"Generated image for prompt: '{self.prompt}'",
                    data={"image_url": image_url, "image_base64": image_b64}
                )
            else:
                self.status = f"Generated image successfully! ({len(image_data)} bytes)"
                return Message(
                    text=f"Generated image for prompt: '{self.prompt}'",
                    data={"image_data": image_data}
                )

        except Exception as e:
            self.status = f"Error: {str(e)}"
            return Message(
                text=f"Failed to generate image: {str(e)}",
                data={"error": str(e)}
            )

    def generate_image(self) -> Message:
        """Synchronous wrapper for async image generation"""
        try:
            # Run the async function
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            result = loop.run_until_complete(self.generate_image_async())
            loop.close()
            return result
        except Exception as e:
            self.status = f"Error: {str(e)}"
            return Message(
                text=f"Failed to generate image: {str(e)}",
                data={"error": str(e)}
            )
