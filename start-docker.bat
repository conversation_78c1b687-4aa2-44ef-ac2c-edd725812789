@echo off
echo Starting ComfyUI + Langflow Docker Environment
echo =============================================

echo.
echo 1. Building and starting containers...
docker-compose up --build -d

echo.
echo 2. Waiting for services to start...
timeout /t 10

echo.
echo 3. Checking service status...
docker-compose ps

echo.
echo =============================================
echo Services are starting up!
echo.
echo ComfyUI will be available at: http://localhost:8188
echo Langflow will be available at: http://localhost:7860
echo.
echo To view logs:
echo   docker-compose logs -f langflow
echo   docker-compose logs -f comfyui
echo.
echo To stop services:
echo   docker-compose down
echo =============================================

pause
