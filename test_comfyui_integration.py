#!/usr/bin/env python3
"""
Test script for ComfyUI Image Generator component
"""

import asyncio
import json
import sys
import os
from comfyui_image_generator import ComfyUIImageGenerator


async def test_comfyui_connection(host="127.0.0.1:8188"):
    """Test basic connection to ComfyUI server"""
    import aiohttp
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(f"http://{host}/") as response:
                if response.status == 200:
                    print(f"✅ ComfyUI server is running at {host}")
                    return True
                else:
                    print(f"❌ ComfyUI server returned status {response.status}")
                    return False
    except Exception as e:
        print(f"❌ Failed to connect to ComfyUI server at {host}: {e}")
        return False


def test_workflow_file(workflow_path="Image Generation.json"):
    """Test if workflow file exists and is valid JSON"""
    try:
        if not os.path.exists(workflow_path):
            print(f"❌ Workflow file not found: {workflow_path}")
            return False
        
        with open(workflow_path, 'r') as f:
            workflow = json.load(f)
        
        # Check for required nodes
        required_classes = ['KSampler', 'CLIPTextEncode', 'SaveImage']
        found_classes = set()
        
        for node_id, node_data in workflow.items():
            if 'class_type' in node_data:
                found_classes.add(node_data['class_type'])
        
        missing_classes = set(required_classes) - found_classes
        if missing_classes:
            print(f"❌ Workflow missing required node types: {missing_classes}")
            return False
        
        print(f"✅ Workflow file is valid: {workflow_path}")
        return True
        
    except json.JSONDecodeError as e:
        print(f"❌ Invalid JSON in workflow file: {e}")
        return False
    except Exception as e:
        print(f"❌ Error reading workflow file: {e}")
        return False


async def test_component_creation():
    """Test creating the ComfyUI component"""
    try:
        component = ComfyUIImageGenerator()
        
        # Set test values
        component.prompt = "a beautiful sunset over mountains"
        component.negative_prompt = "text, watermark"
        component.comfyui_host = "127.0.0.1:8188"
        component.workflow_path = "Image Generation.json"
        component.width = 512
        component.height = 512
        component.steps = 20
        component.cfg = 8.0
        component.timeout = 60
        component.return_base64 = True
        
        print("✅ Component created successfully")
        return component
        
    except Exception as e:
        print(f"❌ Failed to create component: {e}")
        return None


async def test_workflow_modification(component):
    """Test workflow modification"""
    try:
        workflow = component.load_workflow()
        modified_workflow = component.modify_workflow(workflow)
        
        # Check if prompt was injected
        found_prompt = False
        for node_id, node_data in modified_workflow.items():
            if (node_data.get('class_type') == 'CLIPTextEncode' and 
                node_data.get('inputs', {}).get('text') == component.prompt):
                found_prompt = True
                break
        
        if found_prompt:
            print("✅ Workflow modification successful")
            return True
        else:
            print("❌ Prompt not found in modified workflow")
            return False
            
    except Exception as e:
        print(f"❌ Workflow modification failed: {e}")
        return False


async def test_image_generation(component):
    """Test full image generation (requires running ComfyUI server)"""
    try:
        print("🔄 Starting image generation test...")
        result = await component.generate_image_async()
        
        if result.data and 'error' not in result.data:
            print("✅ Image generation successful!")
            print(f"   Status: {component.status}")
            print(f"   Message: {result.text}")
            
            if 'image_base64' in result.data:
                print(f"   Base64 length: {len(result.data['image_base64'])}")
            
            return True
        else:
            print(f"❌ Image generation failed: {result.text}")
            return False
            
    except Exception as e:
        print(f"❌ Image generation error: {e}")
        return False


async def main():
    """Run all tests"""
    print("🧪 Testing ComfyUI Image Generator Integration\n")
    
    # Test 1: Check workflow file
    print("1. Testing workflow file...")
    workflow_ok = test_workflow_file()
    print()
    
    # Test 2: Check ComfyUI connection
    print("2. Testing ComfyUI server connection...")
    server_ok = await test_comfyui_connection()
    print()
    
    # Test 3: Create component
    print("3. Testing component creation...")
    component = await test_component_creation()
    print()
    
    if not component:
        print("❌ Cannot continue tests without component")
        return
    
    # Test 4: Test workflow modification
    print("4. Testing workflow modification...")
    workflow_mod_ok = await test_workflow_modification(component)
    print()
    
    # Test 5: Full integration test (only if server is running)
    if server_ok and workflow_ok:
        print("5. Testing full image generation...")
        generation_ok = await test_image_generation(component)
        print()
    else:
        print("5. Skipping image generation test (ComfyUI server not available)")
        generation_ok = False
        print()
    
    # Summary
    print("📊 Test Summary:")
    print(f"   Workflow file: {'✅' if workflow_ok else '❌'}")
    print(f"   ComfyUI server: {'✅' if server_ok else '❌'}")
    print(f"   Component creation: {'✅' if component else '❌'}")
    print(f"   Workflow modification: {'✅' if workflow_mod_ok else '❌'}")
    print(f"   Image generation: {'✅' if generation_ok else '❌'}")
    
    if all([workflow_ok, server_ok, component, workflow_mod_ok, generation_ok]):
        print("\n🎉 All tests passed! Integration is working correctly.")
        return 0
    else:
        print("\n⚠️  Some tests failed. Check the issues above.")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
