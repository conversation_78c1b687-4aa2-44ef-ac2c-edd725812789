version: '3.8'

services:
  langflow:
    build:
      context: .
      dockerfile: Dockerfile.langflow
    network_mode: "host"
    volumes:
      - .:/app/workspace
      - langflow_data:/app/data
    environment:
      - LANGFLOW_HOST=0.0.0.0
      - LANGFLOW_PORT=7860
      - LANGFLOW_COMPONENTS_PATH=/app/workspace

  comfyui:
    build:
      context: .
      dockerfile: Dockerfile.comfyui
    network_mode: "host"
    volumes:
      - ./models:/app/ComfyUI/models
      - ./output:/app/ComfyUI/output
      - ./input:/app/ComfyUI/input
      - .:/app/workspace
    environment:
      - NVIDIA_VISIBLE_DEVICES=all

volumes:
  langflow_data:
