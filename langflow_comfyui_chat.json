{"data": {"nodes": [{"id": "ChatInput-1", "type": "ChatInput", "position": {"x": 100, "y": 200}, "data": {"type": "ChatInput", "node": {"template": {"input_value": {"required": true, "placeholder": "Type your image prompt here...", "show": true, "multiline": true, "value": "", "password": false, "name": "input_value", "display_name": "Text", "type": "str", "list": false}, "sender": {"required": false, "placeholder": "", "show": true, "multiline": false, "value": "User", "password": false, "name": "sender", "display_name": "Sender Type", "type": "str", "list": false}, "sender_name": {"required": false, "placeholder": "", "show": true, "multiline": false, "value": "User", "password": false, "name": "sender_name", "display_name": "Sender Name", "type": "str", "list": false}, "session_id": {"required": false, "placeholder": "", "show": false, "multiline": false, "value": "", "password": false, "name": "session_id", "display_name": "Session ID", "type": "str", "list": false}}, "description": "A chat input component that collects user input for the chat.", "base_classes": ["Message"], "display_name": "Chat Input", "documentation": "", "custom_fields": {}, "output_types": ["Message"], "pinned": false, "conditional_paths": [], "frozen": false, "outputs": [{"types": ["Message"], "selected": "Message", "name": "message", "display_name": "Message", "method": "message_response"}], "field_order": ["input_value", "sender", "sender_name", "session_id"], "beta": false, "error": null}}}, {"id": "ComfyUIImageGenerator-1", "type": "ComfyUIImageGenerator", "position": {"x": 400, "y": 200}, "data": {"type": "ComfyUIImageGenerator", "node": {"template": {"prompt": {"required": true, "placeholder": "", "show": true, "multiline": true, "value": "", "password": false, "name": "prompt", "display_name": "Prompt", "type": "str", "list": false}, "negative_prompt": {"required": false, "placeholder": "", "show": false, "multiline": false, "value": "text, watermark", "password": false, "name": "negative_prompt", "display_name": "Negative Prompt", "type": "str", "list": false, "advanced": true}, "comfyui_host": {"required": true, "placeholder": "", "show": true, "multiline": false, "value": "127.0.0.1:8188", "password": false, "name": "comfyui_host", "display_name": "ComfyUI Host", "type": "str", "list": false}, "workflow_path": {"required": true, "placeholder": "", "show": false, "multiline": false, "value": "Image Generation.json", "password": false, "name": "workflow_path", "display_name": "Workflow Path", "type": "str", "list": false, "advanced": true}, "width": {"required": false, "placeholder": "", "show": false, "multiline": false, "value": 512, "password": false, "name": "width", "display_name": "<PERSON><PERSON><PERSON>", "type": "int", "list": false, "advanced": true}, "height": {"required": false, "placeholder": "", "show": false, "multiline": false, "value": 512, "password": false, "name": "height", "display_name": "Height", "type": "int", "list": false, "advanced": true}, "steps": {"required": false, "placeholder": "", "show": false, "multiline": false, "value": 20, "password": false, "name": "steps", "display_name": "Steps", "type": "int", "list": false, "advanced": true}, "cfg": {"required": false, "placeholder": "", "show": false, "multiline": false, "value": 8.0, "password": false, "name": "cfg", "display_name": "CFG Scale", "type": "float", "list": false, "advanced": true}, "timeout": {"required": false, "placeholder": "", "show": false, "multiline": false, "value": 60, "password": false, "name": "timeout", "display_name": "Timeout (seconds)", "type": "int", "list": false, "advanced": true}, "return_base64": {"required": false, "placeholder": "", "show": false, "multiline": false, "value": true, "password": false, "name": "return_base64", "display_name": "Return Base64", "type": "bool", "list": false, "advanced": true}}, "description": "Generate images using ComfyUI API with text prompts", "base_classes": ["Message"], "display_name": "ComfyUI Image Generator", "documentation": "", "custom_fields": {}, "output_types": ["Message"], "pinned": false, "conditional_paths": [], "frozen": false, "outputs": [{"types": ["Message"], "selected": "Message", "name": "image_message", "display_name": "Generated Image", "method": "generate_image"}], "field_order": ["prompt", "comfyui_host", "negative_prompt", "workflow_path", "width", "height", "steps", "cfg", "timeout", "return_base64"], "beta": false, "error": null}}}, {"id": "ChatOutput-1", "type": "ChatOutput", "position": {"x": 700, "y": 200}, "data": {"type": "ChatOutput", "node": {"template": {"input_value": {"required": true, "placeholder": "", "show": true, "multiline": true, "value": "", "password": false, "name": "input_value", "display_name": "Text", "type": "str", "list": false}, "sender": {"required": false, "placeholder": "", "show": true, "multiline": false, "value": "Machine", "password": false, "name": "sender", "display_name": "Sender Type", "type": "str", "list": false}, "sender_name": {"required": false, "placeholder": "", "show": true, "multiline": false, "value": "AI", "password": false, "name": "sender_name", "display_name": "Sender Name", "type": "str", "list": false}, "session_id": {"required": false, "placeholder": "", "show": false, "multiline": false, "value": "", "password": false, "name": "session_id", "display_name": "Session ID", "type": "str", "list": false}, "data_template": {"required": false, "placeholder": "", "show": false, "multiline": true, "value": "{text}", "password": false, "name": "data_template", "display_name": "Data Template", "type": "str", "list": false, "advanced": true}, "should_store_message": {"required": false, "placeholder": "", "show": false, "multiline": false, "value": true, "password": false, "name": "should_store_message", "display_name": "Store Messages", "type": "bool", "list": false, "advanced": true}}, "description": "A chat output component that displays chat messages.", "base_classes": ["Message"], "display_name": "Chat Output", "documentation": "", "custom_fields": {}, "output_types": ["Message"], "pinned": false, "conditional_paths": [], "frozen": false, "outputs": [{"types": ["Message"], "selected": "Message", "name": "message", "display_name": "Message", "method": "message_response"}], "field_order": ["input_value", "sender", "sender_name", "session_id", "data_template", "should_store_message"], "beta": false, "error": null}}}], "edges": [{"source": "ChatInput-1", "sourceHandle": "message", "target": "ComfyUIImageGenerator-1", "targetHandle": "prompt", "id": "reactflow__edge-ChatInput-1message-ComfyUIImageGenerator-1prompt"}, {"source": "ComfyUIImageGenerator-1", "sourceHandle": "image_message", "target": "ChatOutput-1", "targetHandle": "input_value", "id": "reactflow__edge-ComfyUIImageGenerator-1image_message-ChatOutput-1input_value"}]}, "description": "AI Image Generation Agent MVP - Langflow to ComfyUI integration", "name": "ComfyUI Image Generator Chat", "last_tested_version": "1.0.0", "endpoint_name": "comfyui-image-chat"}