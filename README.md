# AI Image Generation Agent MVP

A Langflow → ComfyUI integration that enables text-to-image generation through a chat interface.

## 🎯 Overview

This project implements a custom Langflow component that connects to ComfyUI's API to generate images from text prompts. Users can type prompts in a Langflow chat interface and receive generated images directly in the conversation.

## ✨ Features

- **Chat Interface**: Natural conversation flow for image generation
- **Real-time Progress**: WebSocket connection shows generation progress
- **Base64 Image Display**: Images appear directly in the chat
- **Configurable Parameters**: Adjust dimensions, steps, CFG scale, etc.
- **Error Handling**: Graceful handling of timeouts and API errors
- **Async Processing**: Non-blocking image generation

## 🏗️ Architecture

```
User Input → Langflow Chat → ComfyUI Component → ComfyUI API → Generated Image
```

### Components

1. **ChatInput**: Collects user prompts
2. **ComfyUIImageGenerator**: Custom component that handles API communication
3. **ChatOutput**: Displays generated images in chat

### API Flow

1. POST prompt to `http://comfyui_host:8188/prompt`
2. WebSocket connection to track progress via `ws://comfyui_host:8188/ws`
3. Retrieve image via `/history/{prompt_id}` and `/view` endpoints
4. Return base64-encoded image for chat display

## 📋 Prerequisites

### ComfyUI Server
- ComfyUI installed and running
- Default port: 8188
- API access enabled

### Langflow
- Langflow 1.0.0 or higher
- Python 3.8+

### Dependencies
```bash
pip install aiohttp>=3.8.0 websockets>=11.0.0 langflow>=1.0.0
```

## 🚀 Quick Start

### 1. Setup ComfyUI

1. Install ComfyUI following [official instructions](https://docs.comfy.org/)
2. Start ComfyUI server:
   ```bash
   python main.py --listen 0.0.0.0 --port 8188
   ```
3. Verify server is running: `http://localhost:8188`

### 2. Prepare Workflow

1. Create your workflow in ComfyUI
2. Save as API format: **Save (API Format)** button
3. Place the JSON file in your project directory
4. Default filename: `Image Generation.json`

### 3. Install Component

1. Copy `comfyui_image_generator.py` to your Langflow components directory
2. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

### 4. Import Flow

1. Open Langflow
2. Import `langflow_comfyui_chat.json`
3. Configure ComfyUI host if different from `127.0.0.1:8188`
4. Start the flow

### 5. Test Integration

Run the test script to verify everything works:
```bash
python test_comfyui_integration.py
```

## 🔧 Configuration

### Component Parameters

| Parameter | Description | Default | Advanced |
|-----------|-------------|---------|----------|
| `prompt` | Text prompt for image generation | - | No |
| `negative_prompt` | Elements to avoid | "text, watermark" | Yes |
| `comfyui_host` | ComfyUI server address | "127.0.0.1:8188" | No |
| `workflow_path` | Path to workflow JSON | "Image Generation.json" | Yes |
| `width` | Image width in pixels | 512 | Yes |
| `height` | Image height in pixels | 512 | Yes |
| `steps` | Sampling steps | 20 | Yes |
| `cfg` | CFG scale | 8.0 | Yes |
| `timeout` | Generation timeout (seconds) | 60 | Yes |
| `return_base64` | Return base64 for chat display | true | Yes |

### ComfyUI Workflow Requirements

Your workflow must include:
- **KSampler** node for generation
- **CLIPTextEncode** nodes for positive/negative prompts
- **SaveImage** node for output
- **EmptyLatentImage** node for dimensions

## 🧪 Testing

### Manual Testing

1. Start ComfyUI server
2. Run test script: `python test_comfyui_integration.py`
3. Check all tests pass

### Integration Testing

1. Import the Langflow flow
2. Type a prompt like: "a beautiful sunset over mountains"
3. Verify image appears in chat
4. Check generation time is reasonable

## 🐛 Troubleshooting

### Common Issues

**ComfyUI Connection Failed**
- Verify ComfyUI is running on correct port
- Check firewall settings
- Ensure API access is enabled

**Workflow Not Found**
- Check file path is correct
- Verify JSON is valid
- Ensure workflow is in API format

**Generation Timeout**
- Increase timeout parameter
- Check ComfyUI server performance
- Verify workflow complexity

**Image Not Displaying**
- Ensure `return_base64` is true
- Check ChatOutput configuration
- Verify image data in message

### Debug Mode

Enable debug logging in the component:
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 📝 API Reference

### ComfyUIImageGenerator Methods

- `load_workflow()`: Load workflow from JSON file
- `modify_workflow()`: Inject parameters into workflow
- `queue_prompt()`: Submit prompt to ComfyUI
- `wait_for_completion()`: Monitor generation progress
- `get_image()`: Retrieve generated image
- `generate_image()`: Main generation method

### Message Format

Generated images return a Message object:
```python
Message(
    text="Generated image for prompt: 'your prompt'",
    data={
        "image_url": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...",
        "image_base64": "iVBORw0KGgoAAAANSUhEUgAA..."
    }
)
```

## 🔮 Future Enhancements

- [ ] Multiple workflow support
- [ ] Image-to-image generation
- [ ] Batch processing
- [ ] Custom model selection
- [ ] Advanced parameter controls
- [ ] Progress streaming
- [ ] Image history/gallery

## 📄 License

MIT License - see LICENSE file for details.

## 🤝 Contributing

1. Fork the repository
2. Create feature branch
3. Add tests for new functionality
4. Submit pull request

## 📞 Support

- GitHub Issues: Report bugs and feature requests
- Documentation: Check this README and code comments
- Community: Join Langflow Discord for discussions
