services:
  langflow:
    build:
      context: .
      dockerfile: Dockerfile.langflow
    ports:
      - "7860:7860"
    volumes:
      - .:/app/workspace
      - langflow_data:/app/data
    environment:
      - LANGFLOW_HOST=0.0.0.0
      - LANGFLOW_PORT=7860
      - LANGFLOW_COMPONENTS_PATH=/app/workspace
    depends_on:
      - comfyui
    networks:
      - ai_network
    extra_hosts:
      - "host.docker.internal:host-gateway"

  comfyui:
    build:
      context: .
      dockerfile: Dockerfile.comfyui
    ports:
      - "8188:8188"
    volumes:
      - ./models:/app/ComfyUI/models
      - ./output:/app/ComfyUI/output
      - ./input:/app/ComfyUI/input
      - .:/app/workspace
    environment:
      - NVIDIA_VISIBLE_DEVICES=all
    networks:
      - ai_network
    extra_hosts:
      - "host.docker.internal:host-gateway"
    # Uncomment if you have NVIDIA GPU
    # deploy:
    #   resources:
    #     reservations:
    #       devices:
    #         - driver: nvidia
    #           count: 1
    #           capabilities: [gpu]

volumes:
  langflow_data:

networks:
  ai_network:
    driver: bridge
