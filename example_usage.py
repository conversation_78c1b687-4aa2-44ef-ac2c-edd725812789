#!/usr/bin/env python3
"""
Example usage of the ComfyUI Image Generator component
"""

import asyncio
import base64
from comfyui_image_generator import ComfyUIImageGenerator


async def generate_single_image():
    """Example: Generate a single image"""
    print("🎨 Generating single image...")
    
    # Create component
    generator = ComfyUIImageGenerator()
    
    # Configure parameters
    generator.prompt = "a majestic dragon flying over a medieval castle, fantasy art, detailed"
    generator.negative_prompt = "text, watermark, blurry, low quality"
    generator.comfyui_host = "127.0.0.1:8188"
    generator.workflow_path = "Image Generation.json"
    generator.width = 768
    generator.height = 768
    generator.steps = 25
    generator.cfg = 7.5
    generator.timeout = 120
    generator.return_base64 = True
    
    # Generate image
    result = await generator.generate_image_async()
    
    if 'error' not in result.data:
        print(f"✅ Success: {result.text}")
        print(f"📊 Status: {generator.status}")
        
        # Save image to file
        if 'image_base64' in result.data:
            image_data = base64.b64decode(result.data['image_base64'])
            with open('generated_image.png', 'wb') as f:
                f.write(image_data)
            print("💾 Image saved as 'generated_image.png'")
    else:
        print(f"❌ Error: {result.text}")


async def generate_multiple_images():
    """Example: Generate multiple images with different prompts"""
    prompts = [
        "a serene lake at sunset with mountains in the background",
        "a futuristic city with flying cars and neon lights",
        "a cozy cottage in a flower garden, spring time"
    ]
    
    print(f"🎨 Generating {len(prompts)} images...")
    
    for i, prompt in enumerate(prompts, 1):
        print(f"\n🖼️  Image {i}/{len(prompts)}: {prompt[:50]}...")
        
        generator = ComfyUIImageGenerator()
        generator.prompt = prompt
        generator.negative_prompt = "text, watermark, blurry"
        generator.comfyui_host = "127.0.0.1:8188"
        generator.workflow_path = "Image Generation.json"
        generator.width = 512
        generator.height = 512
        generator.steps = 20
        generator.cfg = 8.0
        generator.timeout = 60
        generator.return_base64 = True
        
        result = await generator.generate_image_async()
        
        if 'error' not in result.data:
            print(f"   ✅ Generated successfully")
            
            # Save with numbered filename
            if 'image_base64' in result.data:
                image_data = base64.b64decode(result.data['image_base64'])
                filename = f'generated_image_{i:02d}.png'
                with open(filename, 'wb') as f:
                    f.write(image_data)
                print(f"   💾 Saved as '{filename}'")
        else:
            print(f"   ❌ Failed: {result.text}")


async def test_different_parameters():
    """Example: Test different generation parameters"""
    base_prompt = "a beautiful landscape"
    
    test_configs = [
        {"steps": 10, "cfg": 6.0, "name": "fast"},
        {"steps": 20, "cfg": 8.0, "name": "balanced"},
        {"steps": 30, "cfg": 10.0, "name": "detailed"}
    ]
    
    print(f"🧪 Testing different parameters...")
    
    for config in test_configs:
        print(f"\n⚙️  Testing {config['name']} settings...")
        
        generator = ComfyUIImageGenerator()
        generator.prompt = base_prompt
        generator.negative_prompt = "text, watermark"
        generator.comfyui_host = "127.0.0.1:8188"
        generator.workflow_path = "Image Generation.json"
        generator.width = 512
        generator.height = 512
        generator.steps = config['steps']
        generator.cfg = config['cfg']
        generator.timeout = 60
        generator.return_base64 = True
        
        result = await generator.generate_image_async()
        
        if 'error' not in result.data:
            print(f"   ✅ {config['name'].title()} generation successful")
            
            if 'image_base64' in result.data:
                image_data = base64.b64decode(result.data['image_base64'])
                filename = f'test_{config["name"]}.png'
                with open(filename, 'wb') as f:
                    f.write(image_data)
                print(f"   💾 Saved as '{filename}'")
        else:
            print(f"   ❌ {config['name'].title()} generation failed: {result.text}")


def demonstrate_sync_usage():
    """Example: Using the synchronous wrapper"""
    print("🔄 Demonstrating synchronous usage...")
    
    generator = ComfyUIImageGenerator()
    generator.prompt = "a simple test image, minimalist style"
    generator.negative_prompt = "complex, detailed"
    generator.comfyui_host = "127.0.0.1:8188"
    generator.workflow_path = "Image Generation.json"
    generator.width = 512
    generator.height = 512
    generator.steps = 15
    generator.cfg = 7.0
    generator.timeout = 60
    generator.return_base64 = True
    
    # Use synchronous method
    result = generator.generate_image()
    
    if 'error' not in result.data:
        print(f"✅ Sync generation successful: {result.text}")
        
        if 'image_base64' in result.data:
            image_data = base64.b64decode(result.data['image_base64'])
            with open('sync_generated.png', 'wb') as f:
                f.write(image_data)
            print("💾 Saved as 'sync_generated.png'")
    else:
        print(f"❌ Sync generation failed: {result.text}")


async def main():
    """Main example function"""
    print("🎨 ComfyUI Image Generator Examples")
    print("=" * 50)
    
    examples = [
        ("Single Image Generation", generate_single_image),
        ("Multiple Images", generate_multiple_images),
        ("Parameter Testing", test_different_parameters),
    ]
    
    for name, func in examples:
        print(f"\n📋 Example: {name}")
        print("-" * 30)
        try:
            await func()
            print(f"✅ {name} completed")
        except Exception as e:
            print(f"❌ {name} failed: {e}")
    
    # Sync example
    print(f"\n📋 Example: Synchronous Usage")
    print("-" * 30)
    try:
        demonstrate_sync_usage()
        print("✅ Synchronous usage completed")
    except Exception as e:
        print(f"❌ Synchronous usage failed: {e}")
    
    print("\n🎉 All examples completed!")
    print("\n💡 Tips:")
    print("- Adjust prompts for different styles")
    print("- Experiment with steps and CFG values")
    print("- Try different image dimensions")
    print("- Use negative prompts to avoid unwanted elements")


if __name__ == "__main__":
    asyncio.run(main())
