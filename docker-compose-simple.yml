services:
  langflow:
    image: langflowai/langflow:latest
    ports:
      - "7860:7860"
    volumes:
      - .:/app/workspace
    environment:
      - LANGFLOW_HOST=0.0.0.0
      - LANGFLOW_PORT=7860
    command: langflow run --host 0.0.0.0 --port 7860
    networks:
      - ai_network

  comfyui:
    image: python:3.11-slim
    ports:
      - "8188:8188"
    volumes:
      - .:/app/workspace
    working_dir: /app/workspace
    command: >
      bash -c "
        apt-get update && 
        apt-get install -y git wget &&
        pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu &&
        if [ ! -d ComfyUI ]; then
          git clone https://github.com/comfyanonymous/ComfyUI.git
        fi &&
        cd ComfyUI &&
        pip install -r requirements.txt &&
        python main.py --listen 0.0.0.0 --port 8188
      "
    networks:
      - ai_network

networks:
  ai_network:
    driver: bridge
