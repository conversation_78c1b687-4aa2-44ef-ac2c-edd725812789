import json
import uuid
import base64
import asyncio
import aiohttp
import websockets
from typing import Optional, Dict, Any
from langflow.custom import Component
from langflow.io import StrInput, MultilineInput, Output
from langflow.schema import Message


class ComfyUISimpleTest(Component):
    display_name = "ComfyUI Simple Test"
    description = "Test ComfyUI with embedded workflow"
    icon = "image"
    name = "ComfyUISimpleTest"
    
    inputs = [
        MultilineInput(
            name="prompt",
            display_name="Prompt",
            info="Text prompt for image generation",
            value="a cute cat"
        ),
        StrInput(
            name="comfyui_host",
            display_name="ComfyUI Host",
            info="ComfyUI server host (e.g., 127.0.0.1:8188)",
            value="127.0.0.1:8188"
        )
    ]
    
    outputs = [
        Output(
            display_name="Generated Image",
            name="image_message",
            method="generate_image"
        )
    ]

    def get_embedded_workflow(self) -> Dict[str, Any]:
        """Return the embedded workflow"""
        return {
            "3": {
                "inputs": {
                    "seed": 333447176445413,
                    "steps": 20,
                    "cfg": 8,
                    "sampler_name": "euler",
                    "scheduler": "normal",
                    "denoise": 1,
                    "model": ["4", 0],
                    "positive": ["6", 0],
                    "negative": ["7", 0],
                    "latent_image": ["5", 0]
                },
                "class_type": "KSampler"
            },
            "4": {
                "inputs": {
                    "ckpt_name": "sdXL_v10VAEFix.safetensors"
                },
                "class_type": "CheckpointLoaderSimple"
            },
            "5": {
                "inputs": {
                    "width": 512,
                    "height": 512,
                    "batch_size": 1
                },
                "class_type": "EmptyLatentImage"
            },
            "6": {
                "inputs": {
                    "text": "beautiful scenery nature glass bottle landscape, , purple galaxy bottle,",
                    "clip": ["4", 1]
                },
                "class_type": "CLIPTextEncode"
            },
            "7": {
                "inputs": {
                    "text": "text, watermark",
                    "clip": ["4", 1]
                },
                "class_type": "CLIPTextEncode"
            },
            "8": {
                "inputs": {
                    "samples": ["3", 0],
                    "vae": ["4", 2]
                },
                "class_type": "VAEDecode"
            },
            "9": {
                "inputs": {
                    "filename_prefix": "ComfyUI",
                    "images": ["8", 0]
                },
                "class_type": "SaveImage"
            }
        }

    def modify_workflow(self, workflow: Dict[str, Any]) -> Dict[str, Any]:
        """Modify workflow with current prompt"""
        # Generate random seed
        workflow["3"]["inputs"]["seed"] = uuid.uuid4().int & (1<<63)-1
        
        # Set the prompt in the positive text encode node
        workflow["6"]["inputs"]["text"] = self.prompt
        
        return workflow

    async def queue_prompt(self, workflow: Dict[str, Any], client_id: str) -> str:
        """Queue prompt to ComfyUI and return prompt_id"""
        url = f"http://{self.comfyui_host}/prompt"
        data = {
            "prompt": workflow,
            "client_id": client_id
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.post(url, json=data) as response:
                if response.status != 200:
                    raise Exception(f"Failed to queue prompt: {response.status}")
                result = await response.json()
                return result['prompt_id']

    async def wait_for_completion(self, prompt_id: str, client_id: str) -> None:
        """Wait for prompt completion via WebSocket"""
        ws_url = f"ws://{self.comfyui_host}/ws?clientId={client_id}"
        
        try:
            async with websockets.connect(ws_url) as websocket:
                while True:
                    message = await asyncio.wait_for(websocket.recv(), timeout=60)
                    
                    if isinstance(message, str):
                        data = json.loads(message)
                        
                        if data['type'] == 'executing':
                            node_data = data['data']
                            if node_data['node'] is None and node_data['prompt_id'] == prompt_id:
                                break
                        elif data['type'] == 'progress':
                            progress_data = data['data']
                            self.status = f"Generating... Step {progress_data['value']}/{progress_data['max']}"
                            
        except asyncio.TimeoutError:
            raise Exception("Timeout waiting for image generation")
        except Exception as e:
            raise Exception(f"WebSocket error: {str(e)}")

    async def get_image(self, prompt_id: str) -> bytes:
        """Get generated image from ComfyUI"""
        history_url = f"http://{self.comfyui_host}/history/{prompt_id}"
        
        async with aiohttp.ClientSession() as session:
            async with session.get(history_url) as response:
                if response.status != 200:
                    raise Exception(f"Failed to get history: {response.status}")
                history = await response.json()
        
        if prompt_id not in history:
            raise Exception("Prompt not found in history")
        
        outputs = history[prompt_id]['outputs']
        
        for node_id, node_output in outputs.items():
            if 'images' in node_output:
                for image_info in node_output['images']:
                    if image_info['type'] == 'output':
                        image_url = f"http://{self.comfyui_host}/view"
                        params = {
                            'filename': image_info['filename'],
                            'subfolder': image_info['subfolder'],
                            'type': image_info['type']
                        }
                        
                        async with aiohttp.ClientSession() as session:
                            async with session.get(image_url, params=params) as response:
                                if response.status != 200:
                                    raise Exception(f"Failed to get image: {response.status}")
                                return await response.read()
        
        raise Exception("No output image found")

    async def generate_image_async(self) -> Message:
        """Main async method to generate image"""
        try:
            self.status = "Loading embedded workflow..."
            workflow = self.get_embedded_workflow()
            
            self.status = "Modifying workflow..."
            modified_workflow = self.modify_workflow(workflow)
            
            client_id = str(uuid.uuid4())
            
            self.status = "Queuing prompt..."
            prompt_id = await self.queue_prompt(modified_workflow, client_id)
            
            self.status = "Waiting for completion..."
            await self.wait_for_completion(prompt_id, client_id)
            
            self.status = "Retrieving image..."
            image_data = await self.get_image(prompt_id)
            
            # Convert to base64 for display in chat
            image_b64 = base64.b64encode(image_data).decode('utf-8')
            image_url = f"data:image/png;base64,{image_b64}"
            
            self.status = f"Generated image successfully! ({len(image_data)} bytes)"
            return Message(
                text=f"Generated image for prompt: '{self.prompt}'",
                data={"image_url": image_url, "image_base64": image_b64}
            )
                
        except Exception as e:
            self.status = f"Error: {str(e)}"
            return Message(
                text=f"Failed to generate image: {str(e)}",
                data={"error": str(e)}
            )

    def generate_image(self) -> Message:
        """Synchronous wrapper for async image generation"""
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            result = loop.run_until_complete(self.generate_image_async())
            loop.close()
            return result
        except Exception as e:
            self.status = f"Error: {str(e)}"
            return Message(
                text=f"Failed to generate image: {str(e)}",
                data={"error": str(e)}
            )
